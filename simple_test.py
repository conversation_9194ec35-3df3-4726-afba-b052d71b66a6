#!/usr/bin/env python3
"""
Simple test for Firebase/Firestore connectivity.
"""

import os
import sys

# Add src to path
sys.path.append('src')

try:
    print("Testing Firebase import...")
    import firebase_admin
    from firebase_admin import credentials, firestore
    print("✅ Firebase imports successful")
    
    print("Testing credentials file...")
    cred_file = "wiz-aroma-adama-firebase-adminsdk-fbsvc-c2564abcb8.json"
    if os.path.exists(cred_file):
        print(f"✅ Credentials file found: {cred_file}")
    else:
        print(f"❌ Credentials file not found: {cred_file}")
        sys.exit(1)
    
    print("Testing Firebase initialization...")
    if not firebase_admin._apps:
        cred = credentials.Certificate(cred_file)
        firebase_admin.initialize_app(cred)
        print("✅ Firebase initialized")
    else:
        print("✅ Firebase already initialized")
    
    print("Testing Firestore client...")
    db = firestore.client()
    print("✅ Firestore client created")
    
    print("Testing basic Firestore operation...")
    # Try to read from a test collection
    test_ref = db.collection('test').document('test_doc')
    test_ref.set({'test': 'value', 'timestamp': '2025-06-30'})
    print("✅ Test write successful")
    
    doc = test_ref.get()
    if doc.exists:
        print(f"✅ Test read successful: {doc.to_dict()}")
    else:
        print("❌ Test read failed")
    
    print("\n🎉 All basic tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
