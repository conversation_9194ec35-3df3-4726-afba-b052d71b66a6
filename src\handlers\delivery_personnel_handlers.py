"""
Delivery Personnel handlers for the Wiz Aroma Delivery Bot.
Contains handlers for delivery personnel registration, management, and assignment.
"""

import datetime
import uuid
from telebot import types
from typing import Dict, List, Optional

from src.bot_instance import admin_bot, maintenance_bot
from src.utils.handler_registration import register_handler
from src.config import logger, ADMIN_CHAT_ID, MAINTENANCE_CHAT_ID
from src.data_models import (
    DeliveryPersonnel,
    DeliveryAssignment,
    delivery_personnel,
    delivery_personnel_assignments,
    delivery_personnel_availability,
    delivery_personnel_capacity,
    delivery_personnel_zones,
    delivery_personnel_performance,
    areas_data,
)
from src.data_storage import (
    save_delivery_personnel,
    save_delivery_personnel_assignments,
    save_delivery_personnel_availability,
    save_delivery_personnel_capacity,
    save_delivery_personnel_zones,
    save_delivery_personnel_performance,
    load_delivery_personnel_data,
    load_delivery_personnel_assignments_data,
    load_delivery_personnel_availability_data,
    load_delivery_personnel_capacity_data,
    load_delivery_personnel_zones_data,
    load_delivery_personnel_performance_data,
)


# Temporary storage for registration process
personnel_registration_data = {}


@register_handler("admin", commands=["delivery_personnel"])
def delivery_personnel_menu(message):
    """Show delivery personnel management menu"""
    try:
        # Check if user is authorized
        if str(message.from_user.id) != ADMIN_CHAT_ID:
            admin_bot.reply_to(message, "⚠️ You are not authorized to use this command.")
            return

        markup = types.InlineKeyboardMarkup(row_width=2)
        markup.add(
            types.InlineKeyboardButton("👥 View All Personnel", callback_data="dp_view_all"),
            types.InlineKeyboardButton("➕ Register New", callback_data="dp_register"),
        )
        markup.add(
            types.InlineKeyboardButton("📊 Personnel Status", callback_data="dp_status"),
            types.InlineKeyboardButton("🗺️ Zone Management", callback_data="dp_zones"),
        )
        markup.add(
            types.InlineKeyboardButton("📈 Performance Report", callback_data="dp_performance"),
            types.InlineKeyboardButton("🔄 Refresh Data", callback_data="dp_refresh"),
        )

        admin_bot.send_message(
            message.chat.id,
            "🚚 **Delivery Personnel Management**\n\n"
            "Select an option to manage delivery personnel:",
            reply_markup=markup,
            parse_mode="Markdown"
        )

    except Exception as e:
        logger.error(f"Error in delivery_personnel_menu: {e}")
        admin_bot.reply_to(message, "❌ An error occurred. Please try again.")


@register_handler("admin", handler_type="callback_query", func=lambda call: call.data.startswith("dp_"))
def handle_delivery_personnel_callbacks(call):
    """Handle delivery personnel callback queries"""
    try:
        admin_bot.answer_callback_query(call.id)
        
        if call.data == "dp_view_all":
            view_all_personnel(call)
        elif call.data == "dp_register":
            start_personnel_registration(call)
        elif call.data == "dp_status":
            show_personnel_status(call)
        elif call.data == "dp_zones":
            manage_personnel_zones(call)
        elif call.data == "dp_performance":
            show_performance_report(call)
        elif call.data == "dp_refresh":
            refresh_personnel_data(call)
        elif call.data.startswith("dp_edit_"):
            personnel_id = call.data.replace("dp_edit_", "")
            edit_personnel(call, personnel_id)
        elif call.data.startswith("dp_toggle_"):
            personnel_id = call.data.replace("dp_toggle_", "")
            toggle_personnel_status(call, personnel_id)

    except Exception as e:
        logger.error(f"Error in handle_delivery_personnel_callbacks: {e}")
        admin_bot.send_message(call.message.chat.id, "❌ An error occurred. Please try again.")


def view_all_personnel(call):
    """Display all registered delivery personnel"""
    try:
        # Load fresh data
        personnel_data = load_delivery_personnel_data()
        
        if not personnel_data:
            admin_bot.edit_message_text(
                "📭 No delivery personnel registered yet.\n\n"
                "Use the 'Register New' option to add delivery personnel.",
                call.message.chat.id,
                call.message.message_id
            )
            return

        message_text = "👥 **Registered Delivery Personnel**\n\n"
        
        for personnel_id, data in personnel_data.items():
            personnel = DeliveryPersonnel.from_dict(data)
            status_emoji = {
                "available": "🟢",
                "busy": "🟡", 
                "offline": "🔴"
            }.get(personnel.status, "⚪")
            
            message_text += (
                f"{status_emoji} **{personnel.name}**\n"
                f"📱 {personnel.phone_number}\n"
                f"📍 Areas: {', '.join(personnel.service_areas) if personnel.service_areas else 'None'}\n"
                f"📦 Capacity: {personnel.current_capacity}/{personnel.max_capacity}\n"
                f"⭐ Rating: {personnel.rating:.1f} ({personnel.total_deliveries} deliveries)\n"
                f"✅ Verified: {'Yes' if personnel.is_verified else 'No'}\n\n"
            )

        # Add action buttons
        markup = types.InlineKeyboardMarkup(row_width=1)
        markup.add(types.InlineKeyboardButton("🔙 Back to Menu", callback_data="dp_back_menu"))
        
        admin_bot.edit_message_text(
            message_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode="Markdown"
        )

    except Exception as e:
        logger.error(f"Error in view_all_personnel: {e}")
        admin_bot.send_message(call.message.chat.id, "❌ Error loading personnel data.")


def start_personnel_registration(call):
    """Start the personnel registration process"""
    try:
        user_id = call.from_user.id
        personnel_registration_data[user_id] = {
            "step": "name",
            "data": {}
        }
        
        admin_bot.edit_message_text(
            "👤 **Register New Delivery Personnel**\n\n"
            "Please enter the full name of the delivery person:",
            call.message.chat.id,
            call.message.message_id
        )
        
        # Register next step handler
        admin_bot.register_next_step_handler(
            call.message,
            process_personnel_name
        )

    except Exception as e:
        logger.error(f"Error in start_personnel_registration: {e}")
        admin_bot.send_message(call.message.chat.id, "❌ Error starting registration.")


def process_personnel_name(message):
    """Process the personnel name input"""
    try:
        user_id = message.from_user.id
        
        if user_id not in personnel_registration_data:
            admin_bot.reply_to(message, "❌ Registration session expired. Please start again.")
            return
            
        name = message.text.strip()
        if len(name) < 2:
            admin_bot.reply_to(message, "❌ Name must be at least 2 characters. Please try again:")
            admin_bot.register_next_step_handler(message, process_personnel_name)
            return
            
        personnel_registration_data[user_id]["data"]["name"] = name
        personnel_registration_data[user_id]["step"] = "phone"
        
        admin_bot.reply_to(
            message,
            f"✅ Name: {name}\n\n"
            "📱 Please enter the phone number (with country code, e.g., +251912345678):"
        )
        
        admin_bot.register_next_step_handler(message, process_personnel_phone)

    except Exception as e:
        logger.error(f"Error in process_personnel_name: {e}")
        admin_bot.reply_to(message, "❌ Error processing name. Please try again.")


def process_personnel_phone(message):
    """Process the personnel phone number input"""
    try:
        user_id = message.from_user.id
        
        if user_id not in personnel_registration_data:
            admin_bot.reply_to(message, "❌ Registration session expired. Please start again.")
            return
            
        phone = message.text.strip()
        if len(phone) < 10:
            admin_bot.reply_to(message, "❌ Invalid phone number. Please enter a valid phone number:")
            admin_bot.register_next_step_handler(message, process_personnel_phone)
            return
            
        personnel_registration_data[user_id]["data"]["phone_number"] = phone
        personnel_registration_data[user_id]["step"] = "areas"
        
        # Show available areas
        areas = areas_data.get("areas", [])
        if not areas:
            admin_bot.reply_to(message, "❌ No areas configured. Please configure areas first.")
            return
            
        markup = types.InlineKeyboardMarkup(row_width=2)
        for area in areas:
            markup.add(
                types.InlineKeyboardButton(
                    f"📍 {area['name']}", 
                    callback_data=f"dp_area_{area['id']}"
                )
            )
        markup.add(types.InlineKeyboardButton("✅ Finish Selection", callback_data="dp_areas_done"))
        
        admin_bot.reply_to(
            message,
            f"✅ Phone: {phone}\n\n"
            "🗺️ Please select the service areas (you can select multiple):",
            reply_markup=markup
        )

    except Exception as e:
        logger.error(f"Error in process_personnel_phone: {e}")
        admin_bot.reply_to(message, "❌ Error processing phone number. Please try again.")


def show_personnel_status(call):
    """Show current status of all delivery personnel"""
    try:
        # Load fresh data
        personnel_data = load_delivery_personnel_data()
        availability_data = load_delivery_personnel_availability_data()
        capacity_data = load_delivery_personnel_capacity_data()
        
        if not personnel_data:
            admin_bot.edit_message_text(
                "📭 No delivery personnel registered yet.",
                call.message.chat.id,
                call.message.message_id
            )
            return

        message_text = "📊 **Personnel Status Overview**\n\n"
        
        available_count = 0
        busy_count = 0
        offline_count = 0
        
        for personnel_id, data in personnel_data.items():
            personnel = DeliveryPersonnel.from_dict(data)
            current_capacity = capacity_data.get(personnel_id, 0)
            
            status_emoji = {
                "available": "🟢",
                "busy": "🟡", 
                "offline": "🔴"
            }.get(personnel.status, "⚪")
            
            if personnel.status == "available":
                available_count += 1
            elif personnel.status == "busy":
                busy_count += 1
            else:
                offline_count += 1
            
            message_text += (
                f"{status_emoji} **{personnel.name}**\n"
                f"📦 {current_capacity}/{personnel.max_capacity} orders\n"
                f"📍 {', '.join(personnel.service_areas[:2])}{'...' if len(personnel.service_areas) > 2 else ''}\n\n"
            )

        summary = (
            f"**Summary:**\n"
            f"🟢 Available: {available_count}\n"
            f"🟡 Busy: {busy_count}\n"
            f"🔴 Offline: {offline_count}\n\n"
        )
        
        message_text = summary + message_text
        
        markup = types.InlineKeyboardMarkup(row_width=1)
        markup.add(types.InlineKeyboardButton("🔄 Refresh", callback_data="dp_status"))
        markup.add(types.InlineKeyboardButton("🔙 Back to Menu", callback_data="dp_back_menu"))
        
        admin_bot.edit_message_text(
            message_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=markup,
            parse_mode="Markdown"
        )

    except Exception as e:
        logger.error(f"Error in show_personnel_status: {e}")
        admin_bot.send_message(call.message.chat.id, "❌ Error loading status data.")


def manage_personnel_zones(call):
    """Manage personnel zone assignments"""
    admin_bot.edit_message_text(
        "🗺️ **Zone Management**\n\n"
        "Zone management features coming soon...\n"
        "This will allow you to:\n"
        "• Assign personnel to specific zones\n"
        "• View zone coverage\n"
        "• Optimize delivery routes",
        call.message.chat.id,
        call.message.message_id
    )


def show_performance_report(call):
    """Show performance report for all personnel"""
    admin_bot.edit_message_text(
        "📈 **Performance Report**\n\n"
        "Performance reporting features coming soon...\n"
        "This will show:\n"
        "• Delivery success rates\n"
        "• Average delivery times\n"
        "• Customer ratings\n"
        "• Monthly statistics",
        call.message.chat.id,
        call.message.message_id
    )


def refresh_personnel_data(call):
    """Refresh personnel data from Firebase"""
    try:
        # Reload all delivery personnel data
        global delivery_personnel, delivery_personnel_assignments
        global delivery_personnel_availability, delivery_personnel_capacity
        global delivery_personnel_zones, delivery_personnel_performance
        
        delivery_personnel.clear()
        delivery_personnel.update(load_delivery_personnel_data())
        
        delivery_personnel_assignments.clear()
        delivery_personnel_assignments.update(load_delivery_personnel_assignments_data())
        
        delivery_personnel_availability.clear()
        delivery_personnel_availability.update(load_delivery_personnel_availability_data())
        
        delivery_personnel_capacity.clear()
        delivery_personnel_capacity.update(load_delivery_personnel_capacity_data())
        
        delivery_personnel_zones.clear()
        delivery_personnel_zones.update(load_delivery_personnel_zones_data())
        
        delivery_personnel_performance.clear()
        delivery_personnel_performance.update(load_delivery_personnel_performance_data())
        
        admin_bot.edit_message_text(
            "✅ **Data Refreshed**\n\n"
            f"Loaded:\n"
            f"• {len(delivery_personnel)} personnel records\n"
            f"• {len(delivery_personnel_assignments)} assignments\n"
            f"• {len(delivery_personnel_availability)} availability records\n"
            f"• {len(delivery_personnel_capacity)} capacity records\n\n"
            "Data has been refreshed from Firebase.",
            call.message.chat.id,
            call.message.message_id
        )
        
    except Exception as e:
        logger.error(f"Error in refresh_personnel_data: {e}")
        admin_bot.edit_message_text(
            "❌ Error refreshing data. Please try again.",
            call.message.chat.id,
            call.message.message_id
        )
