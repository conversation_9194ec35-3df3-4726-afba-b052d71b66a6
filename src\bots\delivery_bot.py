"""
Delivery Bot for Wiz Aroma Delivery System
Provides limited order details to delivery personnel for order acceptance.
Access restricted to authorized delivery personnel Telegram IDs.
"""

import telebot
from telebot import types
import datetime
from typing import Dict, List, Any, Optional
import logging

from src.config import (
    DELIVERY_BOT_TOKEN,
    DELIVERY_BOT_AUTHORIZED_IDS,
    logger
)
from src.data_storage import (
    load_awaiting_receipt,
    load_order_status,
    get_restaurant_by_id,
    get_area_by_id
)
from src.firebase_db import get_data
from src.data_models import (
    awaiting_receipt,
    order_status,
    delivery_personnel_assignments
)
from src.utils.delivery_personnel_utils import (
    get_delivery_assignment_by_order,
    get_delivery_personnel_by_telegram_id,
    update_assignment_status,
    get_assignments_for_personnel
)

# Initialize the delivery bot
delivery_bot = telebot.TeleBot(DELIVERY_BOT_TOKEN)

def is_authorized(user_id: int) -> bool:
    """Check if user is authorized to access the delivery bot"""
    return user_id in DELIVERY_BOT_AUTHORIZED_IDS

def access_denied_message(message):
    """Send access denied message to unauthorized users"""
    delivery_bot.reply_to(
        message,
        "🚫 Access Denied\n\nYou are not authorized to use this delivery system."
    )

def get_personnel_by_telegram_id(telegram_id: int):
    """Get delivery personnel by Telegram ID"""
    return get_delivery_personnel_by_telegram_id(str(telegram_id))

@delivery_bot.message_handler(commands=['start'])
def start_command(message):
    """Handle /start command"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    # Check if user is registered as delivery personnel
    personnel = get_personnel_by_telegram_id(user_id)
    if not personnel:
        delivery_bot.reply_to(
            message,
            "❌ You are not registered as delivery personnel. Please contact admin."
        )
        return
    
    welcome_text = f"""
🚚 **Delivery System**
Welcome {personnel.name}!

**Available Commands:**
📋 /orders - View available orders
✅ /accept [order_number] - Accept an order
❌ /decline [order_number] - Decline an order
📦 /myorders - View your assigned orders
🔄 /status [order_number] [status] - Update order status
📍 /location - Update your location

**Order Statuses:**
• `assigned` - Order assigned to you
• `accepted` - You accepted the order
• `picked_up` - Order picked up from restaurant
• `delivered` - Order delivered to customer
• `cancelled` - Order cancelled
    """
    
    delivery_bot.reply_to(message, welcome_text, parse_mode='Markdown')

@delivery_bot.message_handler(commands=['orders'])
def view_available_orders_command(message):
    """View available orders for delivery"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    personnel = get_personnel_by_telegram_id(user_id)
    if not personnel:
        delivery_bot.reply_to(
            message,
            "❌ You are not registered as delivery personnel."
        )
        return
    
    try:
        # Load confirmed orders from Firebase
        confirmed_orders = get_data("confirmed_orders") or {}

        if not confirmed_orders:
            delivery_bot.reply_to(
                message,
                "📭 No orders available for delivery."
            )
            return
        
        # Filter orders that are assigned to this personnel or unassigned
        available_orders = []
        
        for order_number, order_data in confirmed_orders.items():
            assignment = get_delivery_assignment_by_order(order_number)
            
            # Show unassigned orders or orders assigned to this personnel
            if not assignment or assignment.get('personnel_id') == personnel.personnel_id:
                # Get restaurant info
                restaurant_id = order_data.get('restaurant_id')
                restaurant = get_restaurant_by_id(restaurant_id)
                restaurant_name = restaurant['name'] if restaurant else f"Restaurant {restaurant_id}"
                
                # Format order items for delivery personnel (limited info)
                items_text = ""
                if order_data.get('items'):
                    for item in order_data['items']:
                        items_text += f"• {item.get('name', 'Unknown')} (x{item.get('quantity', 1)}) - {item.get('price', 0)} birr\n"
                
                # Create the delivery-specific format as requested
                order_info = f"""
📋 **Order #{order_number}**
📱 Phone: {order_data.get('phone_number', 'N/A')}
🏪 Restaurant: {restaurant_name}
📍 Delivery to: {order_data.get('delivery_location', 'N/A')}

📋 ORDER ITEMS:
{items_text.strip()}

💰 Subtotal: {order_data.get('subtotal', 0)} birr
                """
                
                available_orders.append((order_number, order_info.strip()))
        
        if not available_orders:
            delivery_bot.reply_to(
                message,
                "📭 No orders available for you at the moment."
            )
            return
        
        # Send orders with accept/decline buttons
        for order_number, order_info in available_orders[:5]:  # Limit to 5 orders
            # Create inline keyboard for accept/decline
            markup = types.InlineKeyboardMarkup()
            accept_btn = types.InlineKeyboardButton("✅ Accept", callback_data=f"accept_{order_number}")
            decline_btn = types.InlineKeyboardButton("❌ Decline", callback_data=f"decline_{order_number}")
            markup.row(accept_btn, decline_btn)
            
            delivery_bot.send_message(
                message.chat.id,
                order_info,
                parse_mode='Markdown',
                reply_markup=markup
            )
                
    except Exception as e:
        logger.error(f"Error in view_available_orders_command: {e}")
        delivery_bot.reply_to(
            message,
            "❌ Error retrieving orders. Please try again later."
        )

@delivery_bot.callback_query_handler(func=lambda call: call.data.startswith(('accept_', 'decline_')))
def handle_order_decision(call):
    """Handle order accept/decline decisions"""
    user_id = call.from_user.id
    
    if not is_authorized(user_id):
        delivery_bot.answer_callback_query(call.id, "Access denied")
        return
    
    personnel = get_personnel_by_telegram_id(user_id)
    if not personnel:
        delivery_bot.answer_callback_query(call.id, "Not registered as delivery personnel")
        return
    
    try:
        action, order_number = call.data.split('_', 1)
        
        if action == "accept":
            # Accept the order
            success = update_assignment_status(order_number, personnel.personnel_id, "accepted")
            if success:
                delivery_bot.answer_callback_query(call.id, "✅ Order accepted!")
                delivery_bot.edit_message_text(
                    f"✅ **Order #{order_number} ACCEPTED**\n\n{call.message.text}",
                    call.message.chat.id,
                    call.message.message_id,
                    parse_mode='Markdown'
                )
            else:
                delivery_bot.answer_callback_query(call.id, "❌ Failed to accept order")
                
        elif action == "decline":
            # Decline the order
            success = update_assignment_status(order_number, personnel.personnel_id, "cancelled")
            if success:
                delivery_bot.answer_callback_query(call.id, "❌ Order declined")
                delivery_bot.edit_message_text(
                    f"❌ **Order #{order_number} DECLINED**\n\n{call.message.text}",
                    call.message.chat.id,
                    call.message.message_id,
                    parse_mode='Markdown'
                )
            else:
                delivery_bot.answer_callback_query(call.id, "❌ Failed to decline order")
                
    except Exception as e:
        logger.error(f"Error in handle_order_decision: {e}")
        delivery_bot.answer_callback_query(call.id, "❌ Error processing request")

@delivery_bot.message_handler(commands=['myorders'])
def view_my_orders_command(message):
    """View orders assigned to this delivery personnel"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    personnel = get_personnel_by_telegram_id(user_id)
    if not personnel:
        delivery_bot.reply_to(
            message,
            "❌ You are not registered as delivery personnel."
        )
        return
    
    try:
        # Get assignments for this personnel
        assignments = get_assignments_for_personnel(personnel.personnel_id)
        
        if not assignments:
            delivery_bot.reply_to(
                message,
                "📭 You have no assigned orders."
            )
            return
        
        # Load order data
        awaiting_orders = load_awaiting_receipt()
        
        my_orders = []
        for assignment in assignments:
            order_number = assignment.get('order_number')
            if order_number in awaiting_orders:
                order_data = awaiting_orders[order_number]
                
                # Get restaurant info
                restaurant_id = order_data.get('restaurant_id')
                restaurant = get_restaurant_by_id(restaurant_id)
                restaurant_name = restaurant['name'] if restaurant else f"Restaurant {restaurant_id}"
                
                order_info = f"""
📋 **Order #{order_number}**
📱 Phone: {order_data.get('phone_number', 'N/A')}
🏪 Restaurant: {restaurant_name}
📍 Delivery to: {order_data.get('delivery_location', 'N/A')}
💰 Subtotal: {order_data.get('subtotal', 0)} birr
📊 Status: {assignment.get('status', 'Unknown').title()}
⏰ Assigned: {assignment.get('assigned_at', 'N/A')}
                """
                my_orders.append(order_info.strip())
        
        if my_orders:
            response = "🚚 **Your Assigned Orders:**\n\n" + "\n\n".join(my_orders)
            delivery_bot.reply_to(message, response, parse_mode='Markdown')
        else:
            delivery_bot.reply_to(
                message,
                "📭 You have no current orders."
            )
                
    except Exception as e:
        logger.error(f"Error in view_my_orders_command: {e}")
        delivery_bot.reply_to(
            message,
            "❌ Error retrieving your orders. Please try again later."
        )

@delivery_bot.message_handler(commands=['status'])
def update_order_status_command(message):
    """Update order status"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    personnel = get_personnel_by_telegram_id(user_id)
    if not personnel:
        delivery_bot.reply_to(
            message,
            "❌ You are not registered as delivery personnel."
        )
        return
    
    try:
        # Parse command: /status ORDER_NUMBER STATUS
        command_parts = message.text.split()
        if len(command_parts) < 3:
            delivery_bot.reply_to(
                message,
                "❌ Usage: /status ORDER_NUMBER STATUS\n\nValid statuses: accepted, picked_up, delivered, cancelled"
            )
            return
        
        order_number = command_parts[1]
        new_status = command_parts[2].lower()
        
        valid_statuses = ['accepted', 'picked_up', 'delivered', 'cancelled']
        if new_status not in valid_statuses:
            delivery_bot.reply_to(
                message,
                f"❌ Invalid status. Valid statuses: {', '.join(valid_statuses)}"
            )
            return
        
        # Update the status
        success = update_assignment_status(order_number, personnel.personnel_id, new_status)
        if success:
            delivery_bot.reply_to(
                message,
                f"✅ Order #{order_number} status updated to: {new_status.title()}"
            )
        else:
            delivery_bot.reply_to(
                message,
                f"❌ Failed to update order #{order_number} status. Order may not be assigned to you."
            )
                
    except Exception as e:
        logger.error(f"Error in update_order_status_command: {e}")
        delivery_bot.reply_to(
            message,
            "❌ Error updating order status. Please try again later."
        )

@delivery_bot.message_handler(func=lambda message: True)
def handle_unknown_command(message):
    """Handle unknown commands"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    help_text = """
❓ **Unknown Command**

**Available Commands:**
📋 /orders - View available orders
✅ /accept [order_number] - Accept an order
❌ /decline [order_number] - Decline an order
📦 /myorders - View your assigned orders
🔄 /status [order_number] [status] - Update order status

Example: `/status ORD_20241230_001 picked_up`
    """
    
    delivery_bot.reply_to(message, help_text, parse_mode='Markdown')

def run_delivery_bot():
    """Run the delivery bot"""
    logger.info("Starting Delivery Bot...")
    try:
        delivery_bot.polling(none_stop=True, interval=1)
    except Exception as e:
        logger.error(f"Delivery Bot error: {e}")
        raise

if __name__ == "__main__":
    run_delivery_bot()
